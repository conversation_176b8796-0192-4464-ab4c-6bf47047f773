import * as models from './models';
import { Actions, AnyAction, ComponentDescriptorType, States, SummaryPartShape } from '@topwrite/common';
import ToolBarItem from './components/editor/tool-bar/item';
import Form from './components/form';
import {
    XBlockEditProps,
    XBlockProps,
    XInlineEditProps,
    XInlineProps
} from './components/editor/plugins/x-element/tool';
import { ComponentProps } from 'react';
import './components/editor/custom-types';
import { PromptOptions } from './components/modal/prompt';
import { MenuItemProps } from './pages/status-bar';
import { CatalogMenuItemProps } from './pages/catalog';

type Models = typeof models;

interface AssetHandler {
    onSuccess: (result: { path: string, name: string, size: number; }) => void;
}

declare module '@topwrite/common' {

    export interface PluginMeta {
        installs_count: number;
    }

    export function useSelector<T extends keyof Models>(model: T): States<T, Models>[T];

    export function useActions<T extends keyof Models>(namespace: T): Actions<T, Models>[T];

    export function useModel<T extends keyof Models>(model: T): [States<T, Models>[T], Actions<T, Models>[T]];

    export interface Model {
        getState<T extends keyof Models>(model: T): Generator<AnyAction, States<T, Models>[T]>;
    }

    export interface OptionsStateType {
        preview: string | null;
        feedback: string | null;
        asset: string;
        assistant: {
            base: string;
            authorized: boolean;
        };
        download: string;
        import: string;
        lfs: boolean;
        release: boolean;
        metadata: Record<string, any>;
    }

    export interface ComponentsMap {
        'plugin:setting': ComponentDescriptorType<ComponentProps<typeof Form>>;
        'editor:catalog:menu:item': ComponentDescriptorType<CatalogMenuItemProps>;
        'editor:catalog:part:title': ComponentDescriptorType;
        'editor:catalog:items': ComponentDescriptorType;
        'editor:catalog:part': ComponentDescriptorType;
        'editor:tool': ComponentDescriptorType<ComponentProps<typeof ToolBarItem>>;
        'editor:tool:video:upload': ComponentDescriptorType<AssetHandler>;
        'editor:tool:audio:upload': ComponentDescriptorType<AssetHandler>;
        'editor:tool:image:upload': ComponentDescriptorType<AssetHandler>;
        'editor:tool:attachment:upload': ComponentDescriptorType<AssetHandler>;
        'editor:directive': ComponentDescriptorType;
        'editor:block': ComponentDescriptorType<XBlockProps>;
        'editor:block:edit': ComponentDescriptorType<XBlockEditProps>;
        'editor:inline': ComponentDescriptorType<XInlineProps>;
        'editor:inline:edit': ComponentDescriptorType<XInlineEditProps>;
        'editor:menu:item': ComponentDescriptorType<MenuItemProps>;
    }
}

declare global {
    interface Window {
        TopWriteState: {
            book: {
                id: string;
            },
            options: {
                plugins: {
                    host: string;
                };
            };
        };
    }

    interface TypeToTriggeredEventMap {
        beforeCreatePart: CustomEvent<PromptOptions<SummaryPartShape>>;
        beforeUpdatePart: CustomEvent<PromptOptions<SummaryPartShape>>;
    }

}
